"""
AI服务模块
"""
import json
from zhipuai import ZhipuAI
from fastapi import HTTPException
from config import zhipukey, system_prompt, bs_prompt
from backend.models.schemas import FileInfo, APIResponse


class AIService:
    """AI服务类"""
    
    def __init__(self):
        self.client = ZhipuAI(api_key=zhipukey)
    
    def get_indexes(self, content: str, prompt: str) -> tuple:
        """使用AI生成索引"""
        try:
            completion = self.client.chat.completions.create(
                model="glm-4-air",
                messages=[
                    {"role": "system", "content": prompt},
                    {"role": "user", "content": content}
                ],
                temperature=0.3,
                response_format={"type": "json_object"}
            )
            chinese_blocks = json.loads(completion.choices[0].message.content)
            gjzsd = chinese_blocks.get("text", "")
            gjc = chinese_blocks.get("text2", "")
            return gjzsd, gjc
        except Exception as e:
            print(f"AI生成索引失败: {e}")
            return "", ""
    
    def extract_product_model(self, filename: str) -> str:
        """从文件名提取产品型号"""
        try:
            prompt = """
                - Role: 信息提取与处理专家
                - Background: 用户需要从文件名称中提取产品型号，并以JSON字符串的形式返回。这通常是为了快速整理和分类文件，提高工作效率。
                - Profile: 你是一位精通文本处理和信息提取的专家，擅长从复杂的文本中快速提取关键信息，并能够准确地将其格式化为所需的结构。
                - Skills: 你具备强大的文本解析能力，能够识别和提取特定模式的信息，同时精通JSON格式的生成和处理。
                - Goals: 从文件名称中准确提取产品型号，并以JSON字符串的形式返回。
                - Constrains: 提取的产品型号必须准确无误，且返回的JSON格式必须符合规范。
                - OutputFormat: JSON字符串，例如 {"产品型号":"xface600"}
                - Workflow:
                1. 接收文件名称作为输入。
                2. 分析文件名称，提取其中的产品型号。
                3. 将提取的产品型号格式化为JSON字符串并返回。
                - Examples:
                - 例子1：文件名称 "xface600使用说明书"
                    输出：{"产品型号":"xface600"}
                - 例子2：文件名称 "cm500功能参数介绍及使用说明"
                    输出：{"产品型号":"cm500"}
                - 例子3：文件名称 "pro3000用户手册"
                    输出：{"产品型号":"pro3000"}
                - Initialization: 在第一次对话中，请直接输出以下：您好！我将帮助您从文件名称中提取产品型号并以JSON格式返回。请提供需要处理的文件名称。
            """
            
            completion = self.client.chat.completions.create(
                model="glm-4-air",
                messages=[
                    {"role": "system", "content": prompt},
                    {"role": "user", "content": filename}
                ],
                temperature=0.3,
                response_format={"type": "json_object"}
            )
            
            chinese_blocks = json.loads(completion.choices[0].message.content)
            return chinese_blocks.get("产品型号", "")
        except Exception as e:
            print(f"提取产品型号失败: {e}")
            return ""
    
    async def get_file_info(self, filename: str, file_content: str) -> APIResponse:
        """从文件名和文件内容中提取产品信息"""
        try:
            # 检查文件名是否为空
            if not filename or len(filename.strip()) == 0:
                raise ValueError("文件名不能为空")

            # 构建提示词，根据是否有文件内容调整策略
            prompt = """
                - Role: 信息提取与处理专家
                - Background: 用户需要从文件名称和文件内容中提取产品信息，包括资料类型、产品型号、名称、版本、可接入软件、上市时间等，并以JSON字符串的形式返回。
                - Profile: 你是一位精通文本处理和信息提取的专家，擅长从复杂的文本中快速提取关键信息，并能够准确地将其格式化为所需的结构。
                - Skills: 你具备强大的文本解析能力，能够识别和提取特定模式的信息，同时精通JSON格式的生成和处理。
                - Goals: 从文件名称和文件内容中准确提取产品信息，并以JSON字符串的形式返回。
                - Constrains: 
                  1、提取的信息必须准确无误，且返回的JSON格式必须符合规范。优先使用文件内容中的信息，文件名作为辅助参考。
                  2、文档类型只能为：用户手册、彩页、其他
                - OutputFormat: JSON字符串，必须包含所有字段，例如 {"资料类型":"用户手册","产品型号":"xface600", "名称":"门禁机", "版本":"1.1", "可接入软件":"万傲瑞达V6600","上市时间":"2023.10"}
                - Workflow:
                1. 接收文件名称或文件内容作为输入。
                2. 优先分析文件内容，提取其中的产品信息。
                3. 结合文件名称进行信息补充和验证。
                4. 将提取的信息格式化为JSON字符串并返回，必须包含所有字段：资料类型、产品型号、名称、版本、可接入软件、上市时间。
                - Examples:
                - 例子1：文件名称 "xface600门禁机使用说明书.pdf"，内容包含产品规格等
                    输出：{"资料类型":"用户手册","产品型号":"xface600", "名称":"门禁机", "版本":"", "可接入软件":"","上市时间":""}
                - 例子2：文件名称 "熵基_xFace60-Plus&xFace600-Plus彩页V1.3-20231122.pdf"
                    输出：{"资料类型":"彩页","产品型号":"xFace60-Plus&xFace600-Plus", "名称":"", "版本":"V1.3", "可接入软件":"","上市时间":"2023.11"}
            """

            # 构建用户输入，包含文件名和内容
            user_input = f"文件名：{filename}\n\n文件内容（前2000字符）：{file_content[:2000]}" if file_content else f"文件名：{filename}"

            # 调用智谱AI接口
            try:
                completion = self.client.chat.completions.create(
                    model="glm-4-air",
                    messages=[
                        {"role": "system", "content": prompt},
                        {"role": "user", "content": user_input}
                    ],
                    temperature=0.1,
                    response_format={"type": "json_object"},
                    timeout=30  # 30秒超时
                )
                response_text = completion.choices[0].message.content
                # 尝试解析JSON响应
                try:
                    file_info = json.loads(response_text)
                    return APIResponse(code=200, data=file_info)
                except json.JSONDecodeError as e:
                    print(f"JSON解析失败: {str(e)}, 原始响应: {response_text}")
                    raise ValueError(f"AI返回的结果不是有效的JSON格式: {str(e)}")

            except Exception as e:
                print(f"智谱AI调用失败: {str(e)}")
                raise ValueError(f"AI接口调用失败: {str(e)}")

        except Exception as e:
            print(f"文件信息提取失败: {str(e)}")
            # 返回默认值
            default_info = FileInfo()
            return APIResponse(code=200, data=default_info.model_dump())


# 全局AI服务实例
ai_service = AIService()
