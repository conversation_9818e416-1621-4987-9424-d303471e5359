"""
数据模型定义
"""
from typing import Optional, List, Dict, Any
from pydantic import BaseModel
from datetime import datetime


class DatasetInfo(BaseModel):
    """数据集信息模型"""
    id: str
    name: str
    intro: Optional[str] = ""
    avatar: Optional[str] = "core/dataset/commonDatasetColor"
    updateTime: Optional[str] = ""
    docCount: int = 0
    auditedCount: int = 0
    vectorModel: Optional[str] = ""
    agentModel: Optional[str] = ""


class CollectionInfo(BaseModel):
    """集合信息模型"""
    id: str
    datasetId: str
    datasetname: str
    name: str
    time: datetime
    updateTime: datetime
    audit: str
    cpxh: Optional[str] = ""
    size: int
    description: Optional[str] = ""


class FileInfo(BaseModel):
    """文件信息模型"""
    资料类型: str = "其他"
    产品型号: str = ""
    名称: str = ""
    版本: str = ""
    可接入软件: str = ""
    上市时间: str = ""


class UploadData(BaseModel):
    """上传数据模型"""
    datasetId: str
    parentId: Optional[str] = None
    trainingType: str
    chunkSize: int
    chunkSplitter: Optional[str] = ""
    qaPrompt: Optional[str] = ""
    metadata: Dict[str, Any]


class AuditRequest(BaseModel):
    """审核请求模型"""
    collectionIds: Optional[List[str]] = []
    collectionId: Optional[str] = ""
    token: str
    action: str = "approve"  # approve 或 reject
    rejectReason: Optional[str] = ""


class LoginRequest(BaseModel):
    """登录请求模型"""
    username: str
    password: str


class DatasetData(BaseModel):
    """数据集数据模型"""
    id: str
    q: str
    a: str
    indexes: List[Dict[str, Any]]


class APIResponse(BaseModel):
    """API响应模型"""
    code: int
    message: Optional[str] = ""
    data: Optional[Any] = None
