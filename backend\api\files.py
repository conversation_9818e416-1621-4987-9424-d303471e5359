"""
文件相关API路由
"""
import base64
import pdfplumber
from io import BytesIO
from fastapi import APIRouter, File, Form, UploadFile
from backend.services.file_service import file_service
from backend.services.ai_service import ai_service

router = APIRouter()


@router.post("/uploadfiles/")
async def upload_files(
    file: UploadFile = File(...), 
    type: str = Form(...), 
    data: str = Form(...)
):
    """文件上传"""
    return await file_service.upload_files(file, type, data)


@router.post("/getFileInfo/")
async def get_file_info(body: dict):
    """获取文件信息"""
    try:
        filename = body.get("filename", "")
        content = body.get("content", "")
        if not filename:
            return {"error": "文件名不能为空"}

        # 处理Base64编码的文件内容
        file_content = ''
        if content:
            try:
                # 解码Base64内容
                decoded_bytes = base64.b64decode(content)
                # 将二进制数据转换为内存中的文件对象
                pdf_file = BytesIO(decoded_bytes)
                with pdfplumber.open(pdf_file) as pdf:
                    for page in pdf.pages:
                        text = page.extract_text()  # 自动处理中文编码
                        if text:
                            file_content += text + "\n"
                print(f"成功解码文件内容，长度: {len(file_content)} 字符")

            except Exception as decode_error:
                print(f"解码文件内容失败: {str(decode_error)}")
                file_content = ''

        return await ai_service.get_file_info(filename, file_content)
    except Exception as e:
        print(f"获取文件信息失败: {str(e)}")
        return {"error": f"服务器内部错误: {str(e)}"}

@router.post("/savebs/")
async def save_bs(body: dict):
    """保存标书文件"""
    return await file_service.save_bs(body)
